'use client'

import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Collapsible, CollapsibleContent } from '@/components/ui/collapsible'
import { Search, Filter, X } from 'lucide-react'
import { PackageQueryParams, DataType, NetworkType, ProviderType } from '@/types/package'

interface PackageFiltersProps {
  filters: PackageQueryParams
  onFiltersChange: (filters: PackageQueryParams) => void
  className?: string
}

export function PackageFilters({
  filters,
  onFiltersChange,
  className
}: PackageFiltersProps) {
  const [showAdvanced, setShowAdvanced] = useState(false)

  const handleFilterChange = (key: keyof PackageQueryParams, value: string | number | boolean | undefined) => {
    onFiltersChange({
      ...filters,
      [key]: value,
      page: 1 // 重置页码
    })
  }

  const handleClearFilters = () => {
    onFiltersChange({
      page: 1,
      pageSize: filters.pageSize || 10
    })
  }

  const hasActiveFilters = Object.keys(filters).some(key =>
    key !== 'page' && key !== 'pageSize' && filters[key as keyof PackageQueryParams]
  )

  return (
    <Card className={className}>
      <CardContent className="p-4">
        <div className="space-y-4">
          {/* 基础筛选 */}
          <div className="flex flex-wrap gap-4">
            {/* 搜索框 */}
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索套餐名称或描述..."
                  value={filters.search || ''}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* 提供商筛选 */}
            <div className="w-[150px]">
              <Select
                value={filters.provider || 'all'}
                onValueChange={(value) => handleFilterChange('provider', value === 'all' ? undefined : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="提供商" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部提供商</SelectItem>
                  <SelectItem value={ProviderType.ESIM_ACCESS}>eSIM Access</SelectItem>
                  <SelectItem value={ProviderType.AIRALO}>Airalo</SelectItem>
                  <SelectItem value={ProviderType.KEEPGO}>KeepGo</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 地区筛选 */}
            <div className="w-[150px]">
              <Select
                value={filters.locationCode || 'all'}
                onValueChange={(value) => handleFilterChange('locationCode', value === 'all' ? undefined : value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="地区" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部地区</SelectItem>
                  <SelectItem value="EU">欧洲</SelectItem>
                  <SelectItem value="AS">亚洲</SelectItem>
                  <SelectItem value="NA">北美</SelectItem>
                  <SelectItem value="SA">南美</SelectItem>
                  <SelectItem value="AF">非洲</SelectItem>
                  <SelectItem value="OC">大洋洲</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 操作按钮 */}
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAdvanced(!showAdvanced)}
                className="whitespace-nowrap"
              >
                <Filter className="h-4 w-4 mr-2" />
                高级筛选
              </Button>

              {hasActiveFilters && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleClearFilters}
                  className="whitespace-nowrap"
                >
                  <X className="h-4 w-4 mr-2" />
                  清除筛选
                </Button>
              )}
            </div>
          </div>

          {/* 高级筛选 */}
          <Collapsible open={showAdvanced} onOpenChange={setShowAdvanced}>
            <CollapsibleContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 pt-4 border-t">
                {/* 数据类型 */}
                <div className="space-y-2">
                  <Label>数据类型</Label>
                  <Select
                    value={filters.dataType || 'all'}
                    onValueChange={(value) => handleFilterChange('dataType', value === 'all' ? undefined : value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部类型</SelectItem>
                      <SelectItem value={DataType.LIMITED}>限量流量</SelectItem>
                      <SelectItem value={DataType.UNLIMITED}>无限流量</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* 网络类型 */}
                <div className="space-y-2">
                  <Label>网络类型</Label>
                  <Select
                    value={filters.networkType || 'all'}
                    onValueChange={(value) => handleFilterChange('networkType', value === 'all' ? undefined : value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择网络" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部网络</SelectItem>
                      <SelectItem value={NetworkType.FOUR_G}>4G</SelectItem>
                      <SelectItem value={NetworkType.FIVE_G}>5G</SelectItem>
                      <SelectItem value={NetworkType.LTE}>LTE</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* 价格范围 */}
                <div className="space-y-2">
                  <Label>价格范围 (¥)</Label>
                  <div className="flex gap-2">
                    <Input
                      type="number"
                      placeholder="最低"
                      value={filters.minPrice || ''}
                      onChange={(e) => handleFilterChange('minPrice', e.target.value ? Number(e.target.value) : undefined)}
                      className="w-full"
                    />
                    <Input
                      type="number"
                      placeholder="最高"
                      value={filters.maxPrice || ''}
                      onChange={(e) => handleFilterChange('maxPrice', e.target.value ? Number(e.target.value) : undefined)}
                      className="w-full"
                    />
                  </div>
                </div>

                {/* 有效期范围 */}
                <div className="space-y-2">
                  <Label>有效期 (天)</Label>
                  <div className="flex gap-2">
                    <Input
                      type="number"
                      placeholder="最短"
                      value={filters.minValidityDays || ''}
                      onChange={(e) => handleFilterChange('minValidityDays', e.target.value ? Number(e.target.value) : undefined)}
                      className="w-full"
                    />
                    <Input
                      type="number"
                      placeholder="最长"
                      value={filters.maxValidityDays || ''}
                      onChange={(e) => handleFilterChange('maxValidityDays', e.target.value ? Number(e.target.value) : undefined)}
                      className="w-full"
                    />
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* 数据量范围 */}
                <div className="space-y-2">
                  <Label>数据量范围 (GB)</Label>
                  <div className="flex gap-2">
                    <Input
                      type="number"
                      placeholder="最少"
                      value={filters.minDataVolume ? (filters.minDataVolume / (1024 * 1024 * 1024)).toFixed(1) : ''}
                      onChange={(e) => handleFilterChange('minDataVolume', e.target.value ? Number(e.target.value) * 1024 * 1024 * 1024 : undefined)}
                      className="w-full"
                    />
                    <Input
                      type="number"
                      placeholder="最多"
                      value={filters.maxDataVolume ? (filters.maxDataVolume / (1024 * 1024 * 1024)).toFixed(1) : ''}
                      onChange={(e) => handleFilterChange('maxDataVolume', e.target.value ? Number(e.target.value) * 1024 * 1024 * 1024 : undefined)}
                      className="w-full"
                    />
                  </div>
                </div>

                {/* 功能筛选 */}
                <div className="space-y-2">
                  <Label>支持短信</Label>
                  <Select
                    value={filters.supportsSMS?.toString() || 'all'}
                    onValueChange={(value) => handleFilterChange('supportsSMS', value === 'all' ? undefined : value === 'true')}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部</SelectItem>
                      <SelectItem value="true">支持</SelectItem>
                      <SelectItem value="false">不支持</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>支持充值</Label>
                  <Select
                    value={filters.supportTopUp?.toString() || 'all'}
                    onValueChange={(value) => handleFilterChange('supportTopUp', value === 'all' ? undefined : value === 'true')}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部</SelectItem>
                      <SelectItem value="true">支持</SelectItem>
                      <SelectItem value="false">不支持</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>
        </div>
      </CardContent>
    </Card>
  )
}
