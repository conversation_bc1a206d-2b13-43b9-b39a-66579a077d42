'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import PageGuard from '@/components/layout/PageGuard'
import { StatsActionBar, StatItem, ActionButton } from '@/components/business/stats-action-bar'
import { PackageFilters } from '@/components/business/package-filters'
import { PackageTable } from '@/components/business/package-table'
import { PurchaseDialog } from '@/components/business/purchase-dialog'
import { DataPagination } from '@/components/business/data-pagination'
import { Download, Settings, ShoppingCart } from 'lucide-react'
import { toast } from 'sonner'
import {
  Package as DataPackage,
  PackageQueryParams,
  PackageStats,
  CreateOrderRequest
} from '@/types/package'
import { User } from '@/types/auth'

// 模拟数据
const mockPackages: DataPackage[] = [
  {
    id: 'pkg_001',
    name: '欧洲30天5GB',
    description: '欧洲地区30天有效期5GB流量套餐，支持4G/5G网络',
    price: 9900, // 99.00 CNY
    currency: 'CNY',
    dataVolume: 5 * 1024 * 1024 * 1024, // 5GB
    validityDays: 30,
    locationCodes: ['EU', 'DE', 'FR', 'IT', 'ES'],
    supportsSMS: true,
    dataType: 'LIMITED',
    networkTypes: ['4G', '5G'],
    supportTopUp: true,
    provider: 'eSIM Access'
  },
  {
    id: 'pkg_002',
    name: '亚洲15天3GB',
    description: '亚洲地区15天有效期3GB流量套餐',
    price: 6800,
    currency: 'CNY',
    dataVolume: 3 * 1024 * 1024 * 1024,
    validityDays: 15,
    locationCodes: ['AS', 'CN', 'JP', 'KR'],
    supportsSMS: false,
    dataType: 'LIMITED',
    networkTypes: ['4G'],
    supportTopUp: false,
    provider: 'Airalo'
  },
  {
    id: 'pkg_003',
    name: '全球无限流量',
    description: '全球通用无限流量套餐，7天有效期',
    price: 15800,
    currency: 'CNY',
    dataVolume: 0, // 无限流量
    validityDays: 7,
    locationCodes: ['GLOBAL'],
    supportsSMS: true,
    dataType: 'UNLIMITED',
    networkTypes: ['4G', '5G'],
    supportTopUp: true,
    provider: 'KeepGo'
  }
]

export default function DataPackagesPage() {
  const router = useRouter()

  const [stats, setStats] = useState<PackageStats | null>(null)
  const [packages, setPackages] = useState<DataPackage[]>([])
  const [filters, setFilters] = useState<PackageQueryParams & { search?: string }>({
    page: 1,
    pageSize: 10
  })
  const [loading, setLoading] = useState(true)
  const [purchaseDialogOpen, setPurchaseDialogOpen] = useState(false)
  const [selectedPackage, setSelectedPackage] = useState<DataPackage | null>(null)
  const [purchaseLoading, setPurchaseLoading] = useState(false)
  const [pagination, setPagination] = useState({
    total: 0,
    totalPages: 0
  })

  const loadData = useCallback(async () => {
    setLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 800))

      // 模拟筛选逻辑
      let filteredPackages = [...mockPackages]

      if (filters.search) {
        filteredPackages = filteredPackages.filter(pkg =>
          pkg.name.toLowerCase().includes(filters.search!.toLowerCase()) ||
          pkg.description.toLowerCase().includes(filters.search!.toLowerCase())
        )
      }

      if (filters.provider) {
        filteredPackages = filteredPackages.filter(pkg => pkg.provider === filters.provider)
      }

      if (filters.locationCode) {
        filteredPackages = filteredPackages.filter(pkg =>
          pkg.locationCodes.includes(filters.locationCode!)
        )
      }

      setPackages(filteredPackages)

      // 计算分页信息
      const totalPages = Math.ceil(filteredPackages.length / (filters.pageSize || 10))
      setPagination({
        total: filteredPackages.length,
        totalPages
      })

      setStats({
        totalPackages: mockPackages.length,
        availablePackages: mockPackages.filter(p => p.dataType === 'LIMITED').length,
        unavailablePackages: mockPackages.filter(p => p.dataType === 'UNLIMITED').length,
        providerCount: [...new Set(mockPackages.map(p => p.provider))].length,
        averagePrice: mockPackages.reduce((sum, p) => sum + p.price, 0) / mockPackages.length / 100
      })
    } catch {
      toast.error('加载数据失败')
    } finally {
      setLoading(false)
    }
  }, [filters])

  useEffect(() => {
    loadData()
  }, [loadData])

  // 处理筛选变化
  const handleFiltersChange = (newFilters: PackageQueryParams) => {
    setFilters(newFilters)
  }

  // 处理导出
  const handleExport = () => {
    toast.success('导出功能开发中...')
  }

  // 处理批量设置
  const handleBatchSettings = () => {
    toast.success('批量设置功能开发中...')
  }

  // 处理新购套餐
  const handlePurchaseNew = (packageId: string) => {
    const pkg = packages.find(p => p.id === packageId)
    if (pkg) {
      setSelectedPackage(pkg)
      setPurchaseDialogOpen(true)
    }
  }

  // 处理充值续费
  const handleTopUp = (_packageId: string) => {
    toast.success('充值续费功能开发中...')
  }

  // 处理查看详情
  const handleViewDetails = (packageId: string) => {
    router.push(`/data-packages/${packageId}`)
  }

  // 处理购买订单
  const handlePurchase = async (_request: CreateOrderRequest) => {
    setPurchaseLoading(true)
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1500))
      toast.success('订单创建成功！')
    } catch (error) {
      toast.error('订单创建失败')
      throw error
    } finally {
      setPurchaseLoading(false)
    }
  }

  // 搜索用户
  const handleSearchUsers = async (_query: string): Promise<User[]> => {
    // 模拟用户搜索
    await new Promise(resolve => setTimeout(resolve, 500))
    return [
      {
        id: 'user_001',
        email: '<EMAIL>',
        name: 'John Doe',
        mobile: '+86 138 0013 8000',
        role: 'USER',
        status: 'ACTIVE',
        createdAt: '2023-01-01T00:00:00Z'
      },
      {
        id: 'user_002',
        email: '<EMAIL>',
        name: 'Jane Smith',
        mobile: '+86 139 0013 9000',
        role: 'USER',
        status: 'ACTIVE',
        createdAt: '2023-01-02T00:00:00Z'
      }
    ]
  }

  // 处理分页
  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }))
  }

  const handlePageSizeChange = (pageSize: number) => {
    setFilters(prev => ({ ...prev, pageSize, page: 1 }))
  }

  // 准备统计信息数据
  const getStatsData = (): StatItem[] => {
    if (!stats) return []

    return [
      {
        label: '总套餐数',
        value: stats.totalPackages,
        className: 'text-foreground'
      },
      {
        label: '可用套餐',
        value: stats.availablePackages,
        className: 'text-green-600'
      },
      {
        label: '不可用套餐',
        value: stats.unavailablePackages,
        className: 'text-yellow-600'
      },
      {
        label: '平均价格',
        value: `¥${stats.averagePrice.toLocaleString()}`,
        className: 'text-blue-600'
      }
    ]
  }

  // 准备功能按钮数据
  const getActionButtons = (): ActionButton[] => [
    {
      label: '导出',
      icon: <Download className="h-4 w-4 mr-2" />,
      onClick: handleExport,
      variant: 'outline'
    },
    {
      label: '批量设置',
      icon: <Settings className="h-4 w-4 mr-2" />,
      onClick: handleBatchSettings,
      variant: 'outline'
    },
    {
      label: '购买套餐',
      icon: <ShoppingCart className="h-4 w-4 mr-2" />,
      onClick: () => {
        if (packages.length > 0) {
          setSelectedPackage(packages[0])
          setPurchaseDialogOpen(true)
        }
      },
      variant: 'default'
    }
  ]

  return (
    <PageGuard>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div>
          <h1 className="text-3xl font-bold tracking-tight">数据套餐管理</h1>
          <p className="text-muted-foreground">
            浏览和购买各种数据套餐，为用户提供eSIM服务
          </p>
        </div>

        {/* 统计信息和功能按钮 */}
        <StatsActionBar
          stats={getStatsData()}
          actions={getActionButtons()}
          loading={loading}
        />

        {/* 筛选器 */}
        <PackageFilters
          filters={filters}
          onFiltersChange={handleFiltersChange}
        />

        {/* 数据套餐列表 */}
        <PackageTable
          packages={packages}
          loading={loading}
          onPurchaseNew={handlePurchaseNew}
          onTopUp={handleTopUp}
          onViewDetails={handleViewDetails}
        />

        {/* 分页 */}
        {pagination.totalPages > 1 && (
          <DataPagination
            currentPage={filters.page || 1}
            totalPages={pagination.totalPages}
            totalItems={pagination.total}
            pageSize={filters.pageSize || 10}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
          />
        )}

        {/* 购买对话框 */}
        <PurchaseDialog
          open={purchaseDialogOpen}
          onOpenChange={setPurchaseDialogOpen}
          package={selectedPackage}
          onPurchase={handlePurchase}
          onSearchUsers={handleSearchUsers}
          loading={purchaseLoading}
        />
      </div>
    </PageGuard>
  )
}
